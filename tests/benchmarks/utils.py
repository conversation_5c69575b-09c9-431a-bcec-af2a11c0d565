from __future__ import annotations

import time
import typing as t
from statistics import mean, variance

import numpy as np
from rich.console import Console
from rich.table import Table

P = t.ParamSpec("P")
R = t.TypeVar("R")
OrigFunc = t.Callable[P, R]
DecoratedFunc = t.Callable[P, tuple[float, float]]

def timeit(func: OrigFunc, iteration: int = 3, warmup: int = 1) -> DecoratedFunc:
    """
    Decorator to measure the average execution time and variance over multiple iterations.
    Uses time.perf_counter_ns for nanosecond resolution timing when available.
    """
    def function_timer(
        *args: P.args, **kwargs: P.kwargs
    ) -> tuple[float, float]:
        # Use nanosecond precision if available, otherwise fall back to perf_counter
        timer = getattr(time, 'perf_counter_ns', time.perf_counter)
        divisor = 1e9 if timer is time.perf_counter_ns else 1.0
        
        # Warmup runs to mitigate initialization overhead
        for _ in range(warmup):
            func(*args, **kwargs)

        runtimes = []
        for _ in range(iteration):
            start = timer()
            func(*args, **kwargs)
            end = timer()
            runtime = (end - start) / divisor
            runtimes.append(runtime)

        # Use statistics module for faster calculation on small datasets
        if len(runtimes) < 100:
            return mean(runtimes), variance(runtimes) if len(runtimes) > 1 else 0.0
        return float(np.mean(runtimes)), float(np.var(runtimes))

    return function_timer

def print_table(result: dict[t.Any, tuple[float, float]]) -> None:
    """
    Display benchmark results in a formatted table with sorted results.
    """
    table = Table("Batch Name", "Mean (s)", "Variance (s²)", title="Benchmark Results")
    
    # Sort by mean time for better readability
    sorted_results = sorted(result.items(), key=lambda x: x[1][0])
    for batch_name, (mean_time, var_time) in sorted_results:
        table.add_row(str(batch_name), f"{mean_time:.6f}", f"{var_time:.6f}")
    
    console = Console()
    console.print(table)
