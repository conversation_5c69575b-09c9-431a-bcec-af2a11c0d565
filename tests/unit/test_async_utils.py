import pytest

from ragas.async_utils import run_async_tasks


@pytest.fixture
def tasks():
    """Create a list of async tasks that return their index."""
    async def echo_order(index: int):
        return index

    return [echo_order(i) for i in range(1, 11)]


@pytest.fixture
def expected_results():
    """Expected results for the test tasks."""
    return list(range(1, 11))


@pytest.mark.asyncio
@pytest.mark.parametrize("kwargs,test_description", [
    ({}, "default parameters (unbatched)"),
    ({"batch_size": 3}, "batched execution with batch_size=3"),
    ({"show_progress": False}, "execution without progress bar"),
    ({"batch_size": 5, "show_progress": False}, "batched execution without progress bar"),
])
async def test_run_async_tasks(tasks, expected_results, kwargs, test_description):
    """Test run_async_tasks with various parameter combinations."""
    # Act
    results = run_async_tasks(tasks, **kwargs)

    # Assert
    assert sorted(results) == expected_results, f"Failed for {test_description}"


@pytest.mark.asyncio
async def test_run_async_tasks_empty_list():
    """Test run_async_tasks with empty task list."""
    # Act
    results = run_async_tasks([])

    # Assert
    assert results == []


@pytest.mark.asyncio
async def test_run_async_tasks_single_task():
    """Test run_async_tasks with single task."""
    async def single_task():
        return "single_result"

    # Act
    results = run_async_tasks([single_task()])

    # Assert
    assert results == ["single_result"]
