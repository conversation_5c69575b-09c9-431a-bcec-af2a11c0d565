from langchain_community.document_loaders import DirectoryLoader
from ragas.embeddings import embedding_factory
from ragas.llms import llm_factory
from ragas.testset import TestsetGenerator
import logging
from typing import List, Any, Optional
import time
import os

# 设置日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_testset_generation_e2e() -> None:
    """
    End-to-end 测试：使用 ./docs 下的 Markdown 文件生成测试数据集。
    该测试旨在验证从文档加载到测试数据集生成的完整流程，确保每个步骤都能正确执行。
    测试过程中会记录每个步骤的执行时间和详细日志，以便于性能分析和问题排查。
    """
    start_time = time.time()
    logger.info("开始执行端到端测试...")
    
    try:
        # 检查文档目录是否存在
        # 这是一个前置条件检查，确保测试环境已正确设置
        docs_dir = "./docs"
        if not os.path.exists(docs_dir):
            logger.error(f"文档目录 {docs_dir} 不存在。")
            raise FileNotFoundError(f"文档目录 {docs_dir} 不存在。")
        logger.info(f"确认文档目录 {docs_dir} 存在。")

        # 加载 docs 文件夹下的所有 Markdown 文档
        # 使用 DirectoryLoader 遍历指定目录，加载所有符合条件的文件
        logger.info("开始加载 ./docs 文件夹下的 Markdown 文档...")
        doc_load_start = time.time()
        loader = DirectoryLoader(docs_dir, glob="**/*.md")
        docs: List[Any] = loader.load()
        doc_load_time = time.time() - doc_load_start
        if not docs:
            logger.error("文档加载失败，未找到任何 Markdown 文件。")
            assert docs, f"文档加载失败，未找到任何 Markdown 文件。当前加载文档数: {len(docs)}"
        logger.info(f"成功加载 {len(docs)} 个 Markdown 文档，耗时 {doc_load_time:.2f} 秒。")

        # 初始化 LLM 和嵌入模型
        # LLM 用于生成测试数据，嵌入模型用于语义表示
        logger.info("初始化 LLM 和嵌入模型...")
        init_start = time.time()
        generator_llm = llm_factory("gpt-4o")
        generator_embeddings = embedding_factory()
        init_time = time.time() - init_start
        logger.info(f"初始化完成，耗时 {init_time:.2f} 秒。")

        # 创建并使用测试集生成器生成测试数据集
        # TestsetGenerator 结合 LLM 和嵌入模型，基于加载的文档生成测试用例
        logger.info("创建测试集生成器并开始生成测试数据集...")
        gen_start = time.time()
        generator = TestsetGenerator(llm=generator_llm, embedding_model=generator_embeddings)
        dataset = generator.generate_with_langchain_docs(docs, testset_size=3)
        gen_time = time.time() - gen_start
        if dataset is None:
            logger.error("生成的测试数据集为空。")
            assert dataset is not None, "生成的测试数据集为空，生成过程中可能出现问题，请检查日志。"
        dataset_size = len(dataset) if hasattr(dataset, '__len__') else '未知'
        logger.info(f"测试数据集生成成功，数据集大小: {dataset_size}，耗时 {gen_time:.2f} 秒。")
        
        # 尝试输出数据集样本（如果可能）
        # 输出样本数据有助于直观了解生成结果，便于调试和验证
        try:
            if dataset_size != '未知' and dataset_size > 0:
                sample_data = dataset[0] if isinstance(dataset, list) else getattr(dataset, 'data', None)
                if sample_data:
                    logger.info(f"数据集样本 (第一个元素): {sample_data}")
        except Exception as sample_err:
            logger.warning(f"无法输出数据集样本: {str(sample_err)}")

        total_time = time.time() - start_time
        logger.info(f"端到端测试完成，总耗时 {total_time:.2f} 秒。")
        
        # 输出标识，用于验证调用结果
        # 此标识用于确认测试执行到最后一步，便于自动化验证
        print("111111")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)
        raise
