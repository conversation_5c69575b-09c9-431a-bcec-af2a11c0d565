<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>今日日期与时间</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .time-display {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .time-item {
            font-size: 1.2em;
            margin: 10px 0;
            color: #34495e;
        }
        .digital-clock {
            font-size: 2.5em;
            font-weight: bold;
            color: #2980b9;
            margin: 20px 0;
        }
        .date-info {
            font-size: 1.5em;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>今日日期与时间</h1>
        <div class="time-display">
            <div id="digitalClock" class="digital-clock"></div>
            <div id="dateInfo" class="date-info"></div>
            <div id="weekInfo" class="time-item"></div>
            <div id="lunarDate" class="time-item"></div>
        </div>
    </div>
    <script>
        const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        
        function updateDateTime() {
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth() + 1).padStart(2, '0');
            const d = String(now.getDate()).padStart(2, '0');
            const hh = String(now.getHours()).padStart(2, '0');
            const mm = String(now.getMinutes()).padStart(2, '0');
            const ss = String(now.getSeconds()).padStart(2, '0');
            const week = weekDays[now.getDay()];

            // 更新数字时钟
            document.getElementById('digitalClock').textContent = `${hh}:${mm}:${ss}`;
            
            // 更新日期信息
            document.getElementById('dateInfo').textContent = `${y}年${m}月${d}日`;
            
            // 更新星期信息
            document.getElementById('weekInfo').textContent = week;
        }

        // 初始化显示
        updateDateTime();
        
        // 每秒更新一次
        setInterval(updateDateTime, 1000);
    </script>
</body>
</html>
<!-- 111 -->