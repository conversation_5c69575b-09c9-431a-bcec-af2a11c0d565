import React, { useCallback } from 'react';
import { useConversationStore, useAppConfigStore } from '@/store';
import { useSendMessage } from '@/hooks';
import Icon from '@/components/Icon';
import SuggestQuestions from '@/components/SuggestQuestions';
import { reportUserAction, ReportOpt } from '@/utils/report';

interface Props {
  scrollToBottom: (behavior?: ScrollBehavior) => void;
}

export default function GuessQuestion(props: Props) {
  const suggestQuestions = useConversationStore((state) => state.suggestQuestions);
  const updateAppConfig = useAppConfigStore((state) => state.update);
  const { scrollToBottom } = props;

  // 优化后的 afterSend 回调函数
  const afterSend = useCallback(
    (id: number) => {
      const opt: ReportOpt<'chat'> = {
        key: 'chat',
        type: 'old-chat',
      };
      reportUserAction(id, opt);
      scrollToBottom('smooth');
      updateAppConfig({ scrollSmoothSingle: true }); // 直接更新配置
    },
    [updateAppConfig, scrollToBottom],
  );

  // 优化后的 onBeforeSend 回调函数
  const onBeforeSend = useCallback(() => {
    updateAppConfig({ scrollSmoothSingle: false });
  }, [updateAppConfig]);

  const { sendMsg } = useSendMessage({
    isCreate: false,
    afterSend,
    beforeSend: onBeforeSend,
  });

  if (!suggestQuestions?.length) return null;

  return (
    <div className="mt-6">
      <div className="flex items-center gap-2">
        <Icon name="kwaipilot_system_guess" className="!size-4 !text-fill-secondary" />
        <div className="text-secondary text-[16px] font-medium leading-6 opacity-80">猜你想问</div>
      </div>
      <div className="mt-5">
        <SuggestQuestions suggestQuestions={suggestQuestions} onClickQuestion={sendMsg} />
      </div>
    </div>
  );
}
