import typing as t
from dataclasses import dataclass

import numpy as np

from ragas.testset.graph import KnowledgeGraph, NodeType, Relationship
from ragas.testset.transforms.base import RelationshipBuilder


@dataclass
class CosineSimilarityBuilder(RelationshipBuilder):
    property_name: str = "embedding"
    new_property_name: str = "cosine_similarity"
    threshold: float = 0.9

    def _find_similar_embedding_pairs(
        self, embeddings: np.ndarray, threshold: float
    ) -> t.List[t.Tuple[int, int, float]]:
        try:
            from scipy.spatial import cKDTree
        except ImportError:
            raise ImportError(
                "scipy is not installed. Please install it with 'pip install scipy'"
            )

        # Normalize the embeddings
        norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
        # handle zero-norm vectors to avoid division by zero
        norms[norms == 0] = 1e-9
        normalized = embeddings / norms

        # Build KD-Tree for faster pair finding
        tree = cKDTree(normalized)

        # For normalized vectors, cosine similarity and Euclidean distance are related.
        # d = sqrt(2 * (1 - s)) where d is Euclidean distance and s is cosine similarity.
        # We want s >= threshold, which means d <= sqrt(2 * (1 - threshold)).
        # Clamp threshold to avoid math domain error with values > 1.0 or < -1.0
        clamped_threshold = np.clip(threshold, -1.0, 1.0)
        distance_threshold = np.sqrt(2 * (1 - clamped_threshold))

        # Find pairs within the distance threshold
        similar_pairs_indices = tree.query_pairs(
            r=distance_threshold, output_type="ndarray"
        )

        if similar_pairs_indices.size == 0:
            return []

        # Calculate exact cosine similarity for the found pairs
        i_indices = similar_pairs_indices[:, 0]
        j_indices = similar_pairs_indices[:, 1]

        # vectorized dot product
        similarities = np.sum(normalized[i_indices] * normalized[j_indices], axis=1)

        # Even with the distance threshold, due to floating point inaccuracies,
        # it's safer to filter based on the original threshold.
        mask = similarities >= threshold

        final_i = i_indices[mask]
        final_j = j_indices[mask]
        final_sim = similarities[mask]

        return list(zip(final_i.tolist(), final_j.tolist(), final_sim.tolist()))

    async def transform(self, kg: KnowledgeGraph) -> t.List[Relationship]:
        if self.property_name is None:
            self.property_name = "embedding"

        embeddings = []
        for node in kg.nodes:
            embedding = node.get_property(self.property_name)
            if embedding is None:
                raise ValueError(f"Node {node.id} has no {self.property_name}")
            embeddings.append(embedding)

        similar_pairs = self._find_similar_embedding_pairs(
            np.array(embeddings), self.threshold
        )

        return [
            Relationship(
                source=kg.nodes[i],
                target=kg.nodes[j],
                type="cosine_similarity",
                properties={self.new_property_name: similarity_float},
                bidirectional=True,
            )
            for i, j, similarity_float in similar_pairs
        ]


@dataclass
class SummaryCosineSimilarityBuilder(CosineSimilarityBuilder):
    property_name: str = "summary_embedding"
    new_property_name: str = "summary_cosine_similarity"
    threshold: float = 0.1

    def filter(self, kg: KnowledgeGraph) -> KnowledgeGraph:
        """
        Filters the knowledge graph to only include nodes with a summary embedding.
        """
        nodes = []
        for node in kg.nodes:
            if node.type == NodeType.DOCUMENT:
                emb = node.get_property(self.property_name)
                if emb is None:
                    raise ValueError(f"Node {node.id} has no {self.property_name}")
                nodes.append(node)
        return KnowledgeGraph(nodes=nodes)

    async def transform(self, kg: KnowledgeGraph) -> t.List[Relationship]:
        embeddings = [
            node.get_property(self.property_name)
            for node in kg.nodes
            if node.get_property(self.property_name) is not None
        ]
        if not embeddings:
            raise ValueError(f"No nodes have a valid {self.property_name}")
        similar_pairs = self._find_similar_embedding_pairs(
            np.array(embeddings), self.threshold
        )
        return [
            Relationship(
                source=kg.nodes[i],
                target=kg.nodes[j],
                type="summary_cosine_similarity",
                properties={self.new_property_name: similarity_float},
                bidirectional=True,
            )
            for i, j, similarity_float in similar_pairs
        ]
