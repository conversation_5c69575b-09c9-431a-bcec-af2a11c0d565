from __future__ import annotations

import logging
import typing as t
from dataclasses import dataclass, field
from functools import lru_cache

import numpy as np
from pydantic import BaseModel, Field

from ragas.dataset_schema import SingleTurnSample
from ragas.metrics._answer_similarity import AnswerSimilarity
from ragas.metrics._faithfulness import (
    StatementGeneratorInput,
    StatementGeneratorOutput,
    StatementGeneratorPrompt,
)
from ragas.metrics.base import (
    MetricOutputType,
    MetricType,
    MetricWithEmbeddings,
    MetricWithLLM,
    SingleTurnMetric,
)
from ragas.metrics.utils import fbeta_score
from ragas.prompt import PydanticPrompt
from ragas.run_config import RunConfig

if t.TYPE_CHECKING:
    from langchain_core.callbacks import Callbacks

logger = logging.getLogger(__name__)


class QuestionAnswerGroundTruth(BaseModel):
    """包含问题、答案和参考答案（真值）的输入模型"""
    question: str = Field(description="用户提出的原始问题")
    answer: list[str] = Field(description="模型生成的答案陈述列表")
    ground_truth: list[str] = Field(description="参考答案（真值）陈述列表")


class StatementsWithReason(BaseModel):
    """语句及其分类理由"""
    statement: str = Field(description="一个被分类的陈述句")
    reason: str = Field(description="该陈述句分类的详细理由")


class ClassificationWithReason(BaseModel):
    """分类结果，包含TP、FP和FN的语句及理由"""
    TP: list[StatementsWithReason] = Field(
        default_factory=list,
        description="真阳性：答案中存在的陈述，也直接由一个或多个参考答案中的陈述支持"
    )
    FP: list[StatementsWithReason] = Field(
        default_factory=list,
        description="假阳性：答案中存在的陈述，但未被任何参考答案中的陈述直接支持"
    )
    FN: list[StatementsWithReason] = Field(
        default_factory=list,
        description="假阴性：参考答案中存在的陈述，但未在答案中出现"
    )


class CorrectnessClassifier(
    PydanticPrompt[QuestionAnswerGroundTruth, ClassificationWithReason]
):
    """答案正确性分类器提示"""
    instruction = "给定一个参考答案和答案陈述，分析每个陈述并将其分类为以下类别之一：TP（真阳性）：答案中存在的陈述，也直接由一个或多个参考答案中的陈述支持；FP（假阳性）：答案中存在的陈述，但未被任何参考答案中的陈述直接支持；FN（假阴性）：参考答案中存在的陈述，但未在答案中出现。每个陈述只能属于一个类别。为每个分类提供理由。"
    input_model = QuestionAnswerGroundTruth
    output_model = ClassificationWithReason
    examples = [
        (
            QuestionAnswerGroundTruth(
                question="太阳的能量来源是什么？它的主要功能是什么？",
                answer=[
                    "太阳的能量来源于核裂变，类似于地球上的核反应堆。",
                    "太阳的主要功能是向太阳系提供光。",
                ],
                ground_truth=[
                    "太阳的能量来源于核聚变，氢原子融合形成氦。",
                    "太阳核心的核聚变过程释放出巨大的能量。",
                    "太阳的能量为地球提供了光和热，这对生命至关重要。",
                    "太阳的光对地球的气候系统起着关键作用。",
                    "阳光有助于驱动天气模式和海洋洋流。",
                ],
            ),
            ClassificationWithReason(
                TP=[
                    StatementsWithReason(
                        statement="太阳的主要功能是向太阳系提供光。",
                        reason="这个陈述在某种程度上得到了参考答案的支持，提到了太阳提供的光及其作用，尽管它更广泛地关注了太阳的能量。",
                    )
                ],
                FP=[
                    StatementsWithReason(
                        statement="太阳的能量来源于核裂变，类似于地球上的核反应堆。",
                        reason="这个陈述是不正确的，并且与参考答案相矛盾，参考答案指出太阳的能量来源于核聚变。",
                    )
                ],
                FN=[
                    StatementsWithReason(
                        statement="太阳的能量来源于核聚变，氢原子融合形成氦。",
                        reason="这个准确描述了太阳能量来源的陈述未包含在答案中。",
                    ),
                    StatementsWithReason(
                        statement="太阳核心的核聚变过程释放出巨大的能量。",
                        reason="这个过程及其重要性未在答案中提及。",
                    ),
                    StatementsWithReason(
                        statement="太阳的能量为地球提供了光和热，这对生命至关重要。",
                        reason="答案仅提到了光，忽略了热及其对生命的必要性，而参考答案涵盖了这些内容。",
                    ),
                    StatementsWithReason(
                        statement="太阳的光对地球的气候系统起着关键作用。",
                        reason="这个关于太阳光对地球气候系统更广泛影响的陈述未在答案中提及。",
                    ),
                    StatementsWithReason(
                        statement="阳光有助于驱动天气模式和海洋洋流。",
                        reason="答案忽略了阳光对天气模式和海洋洋流的影响。",
                    ),
                ],
            ),
        ),
        (
            QuestionAnswerGroundTruth(
                question="水的沸点是多少？",
                answer=[
                    "水的沸点在海平面上是100摄氏度"
                ],
                ground_truth=[
                    "水的沸点在海平面上是100摄氏度（212华氏度）。",
                    "水的沸点会随着海拔的变化而变化。",
                ],
            ),
            ClassificationWithReason(
                TP=[
                    StatementsWithReason(
                        statement="水的沸点在海平面上是100摄氏度",
                        reason="这个陈述直接得到了参考答案的支持，参考答案指定了水在海平面上的沸点为100摄氏度。",
                    )
                ],
                FP=[],
                FN=[
                    StatementsWithReason(
                        statement="水的沸点会随着海拔的变化而变化。",
                        reason="这个关于水的沸点如何随海拔变化的额外信息未在答案中提及。",
                    )
                ],
            ),
        ),
    ]


@dataclass
class AnswerCorrectness(MetricWithLLM, MetricWithEmbeddings, SingleTurnMetric):
    """
    测量答案相对于参考答案的正确性，结合事实性（语句存在）和语义相似度（语义相似度）的权重打分。
    
    该指标通过两个主要步骤计算得分：
    1. 事实性评分：将答案分解为陈述句，然后将其分类为TP（真阳性）、FP（假阳性）、FN（假阴性），
       并使用F-beta分数计算事实性得分
    2. 语义相似度评分：使用嵌入模型计算答案与参考答案的语义相似度
    最终分数是这两个评分的加权平均值。

    属性
    ----------
    name: str
        指标名称
    weights: list[float]
        权重列表 [事实性权重, 相似度权重]，默认 [0.75, 0.25]
    beta: float
        F-beta分数参数。>1偏重召回，<1偏重精度
    answer_similarity: Optional[AnswerSimilarity]
        语义相似度评分实例
    max_retries: int
        LLM调用的最大重试次数
    cache_size: int
        LRU缓存大小，用于缓存陈述生成结果
    retry_on_error: bool
        是否在错误时重试LLM调用
    """
    name: str = "answer_correctness"
    _required_columns: t.Dict[MetricType, t.Set[str]] = field(
        default_factory=lambda: {
            MetricType.SINGLE_TURN: {"user_input", "response", "reference"}
        }
    )
    output_type: MetricOutputType = MetricOutputType.CONTINUOUS
    correctness_prompt: PydanticPrompt = field(default_factory=CorrectnessClassifier)
    statement_generator_prompt: PydanticPrompt = field(default_factory=StatementGeneratorPrompt)
    weights: list[float] = field(default_factory=lambda: [0.75, 0.25])
    beta: float = 1.0
    answer_similarity: t.Optional[AnswerSimilarity] = None
    max_retries: int = 1
    cache_size: int = 128
    retry_on_error: bool = True
    def __post_init__(self):
        """初始化后验证参数"""
        # 验证权重设置
        if len(self.weights) != 2:
            raise ValueError(
                "权重列表必须包含两个值，第一个为事实性权重，第二个为语义相似度权重"
            )
        if all(w == 0 for w in self.weights):
            raise ValueError("至少有一个权重必须大于0")
        if not all(w >= 0 for w in self.weights):
            raise ValueError("所有权重必须为非负数")
            
        # 验证beta参数
        if not isinstance(self.beta, (float, int)):
            raise ValueError(
                "Beta必须是浮点数或整数。Beta > 1时更重视召回率，Beta < 1时更重视精度"
            )
        self.beta = float(self.beta)
        
        # 设置LRU缓存，提高性能
        self._create_simplified_statements = lru_cache(maxsize=self.cache_size)(
            self._create_simplified_statements_implementation
        )

    def init(self, run_config: RunConfig):
        """初始化运行配置，如果需要语义相似度且未设置，则创建AnswerSimilarity实例"""
        super().init(run_config)
        # 只在需要时初始化AnswerSimilarity，节省资源
        if self.answer_similarity is None and self.weights[1] > 0:
            self.answer_similarity = AnswerSimilarity(embeddings=self.embeddings)
            logger.debug("已初始化AnswerSimilarity组件")

    def _compute_statement_presence(
        self, prediction: ClassificationWithReason
    ) -> float:
        """
        计算语句存在性得分（事实性）
        
        参数
        ----------
        prediction: ClassificationWithReason
            包含TP、FP、FN分类的语句
            
        返回
        ----------
        float
            F-beta分数，范围为0到1
        """
        tp = len(prediction.TP)
        fp = len(prediction.FP)
        fn = len(prediction.FN)
        return fbeta_score(tp, fp, fn, self.beta)

    async def _create_simplified_statements_implementation(
        self, question: str, text: str, callbacks: t.Optional[t.Any] = None
    ) -> StatementGeneratorOutput:
        """
        将文本转换为简化的陈述句列表的实际实现
        
        参数
        ----------
        question: str
            问题文本
        text: str
            答案文本
        callbacks: Optional[Any]
            回调函数
            
        返回
        ----------
        StatementGeneratorOutput
            包含陈述句列表的输出
        """
        assert self.llm is not None, "LLM未设置"
        prompt_input = StatementGeneratorInput(question=question, answer=text)
        
        # 添加重试机制提高稳定性
        attempts = 0
        while attempts <= self.max_retries:
            try:
                return await self.statement_generator_prompt.generate(
                    llm=self.llm,
                    data=prompt_input,
                    callbacks=callbacks,
                )
            except Exception as e:
                attempts += 1
                if attempts > self.max_retries or not self.retry_on_error:
                    logger.error(f"生成陈述句失败: {str(e)}", exc_info=True)
                    # 返回空结果而非抛出异常，避免整个评估失败
                    return StatementGeneratorOutput(statements=[])
                logger.warning(f"生成陈述句失败，正在重试 ({attempts}/{self.max_retries}): {str(e)}")
        
        # 不应到达这里，但作为安全措施添加
        return StatementGeneratorOutput(statements=[])

    async def _single_turn_ascore(
        self, sample: SingleTurnSample, callbacks: t.Optional[t.Any] = None
    ) -> float:
        """计算单轮对话的分数"""
        row = sample.to_dict()
        return await self._ascore(row, callbacks)

    async def _ascore(self, row: t.Dict, callbacks: t.Optional[t.Any] = None) -> float:
        """
        计算答案正确性得分
        
        参数
        ----------
        row: Dict
            包含user_input、response和reference的字典
        callbacks: Optional[Any]
            回调函数
            
        返回
        ----------
        float
            正确性得分，范围为0到1
        """
        assert self.llm is not None, "LLM未设置"
        question = row.get("user_input", "")
        
        # 增强输入验证
        if not question:
            logger.warning("缺少用户问题，无法评估正确性")
            return float("nan")
            
        response = row.get("response", "")
        if not response:
            logger.warning("缺少回答文本，无法评估正确性")
            return float("nan")
            
        reference = row.get("reference", "")
        if not reference:
            logger.warning("缺少参考答案，无法评估正确性")
            return float("nan")

        try:
            # 将答案和参考文本转换为陈述句
            statements = {}
            for item, text in [("response", response), ("reference", reference)]:
                statements_output = await self._create_simplified_statements(
                    question, text, callbacks
                )
                statements[item] = statements_output.statements or []  # 确保为非空列表

            # 检查陈述句生成是否成功
            if not statements["reference"] and not statements["response"]:
                logger.warning("无法从回答和参考文本中生成有效陈述句")
                return float("nan")
                
            # 如果只有一边有陈述句，计算基本分数
            if not statements["reference"]:
                logger.warning("参考答案未生成有效陈述句，默认得分为0")
                return 0.0
                
            if not statements["response"]:
                logger.warning("回答未生成有效陈述句，默认得分为0")
                return 0.0

            # 使用LLM进行分类
            ground_truth = statements["reference"]
            answer = statements["response"]
            
            # 添加重试机制提高稳定性
            attempts = 0
            answers = None
            while attempts <= self.max_retries:
                try:
                    answers = await self.correctness_prompt.generate(
                        llm=self.llm,
                        data=QuestionAnswerGroundTruth(
                            question=question,
                            answer=answer,
                            ground_truth=ground_truth,
                        ),
                        callbacks=callbacks,
                    )
                    break
                except Exception as e:
                    attempts += 1
                    if attempts > self.max_retries or not self.retry_on_error:
                        logger.error(f"生成分类结果失败: {str(e)}", exc_info=True)
                        return float("nan")
                    logger.warning(f"生成分类结果失败，正在重试 ({attempts}/{self.max_retries}): {str(e)}")
                    
            # 验证LLM输出
            if answers is None:
                logger.warning("LLM生成的分类结果为None")
                return float("nan")
                
            # 计算事实性得分
            f1_score = self._compute_statement_presence(answers)
            
            # 计算语义相似度得分（如果需要）
            similarity_score = 0.0
            if self.weights[1] > 0:
                assert self.answer_similarity is not None, "AnswerSimilarity未设置"
                try:
                    similarity_score = await self.answer_similarity.ascore(row, callbacks=callbacks)
                except Exception as e:
                    logger.warning(f"计算语义相似度失败: {str(e)}")
                    # 如果相似度计算失败，则只使用事实性得分
                    return f1_score
            # 计算加权平均得分
            score = float(np.average([f1_score, similarity_score], weights=self.weights))
            
            # 添加详细日志
            logger.debug(
                f"答案正确性得分: {score:.4f} (f1={f1_score:.4f}, sim={similarity_score:.4f}, w={self.weights})"
            )
            
            # 输出分类结果统计信息，便于调试
            logger.debug(
                f"分类结果: TP={len(answers.TP)}, FP={len(answers.FP)}, FN={len(answers.FN)}"
            )
            
            return score
        except Exception as e:
            logger.error(f"计算答案正确性时发生错误: {e}", exc_info=True)
            return float("nan")


# 实例化默认的答案正确性评估器
answer_correctness = AnswerCorrectness()
