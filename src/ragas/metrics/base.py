from __future__ import annotations

import asyncio
import logging
import typing as t
from abc import ABC, abstractmethod
from collections import Counter
from dataclasses import dataclass, field
from enum import Enum

from pydantic import ValidationError
from tqdm import tqdm

from ragas._analytics import EvaluationEvent, _analytics_batcher
from ragas.callbacks import ChainType, new_group
from ragas.dataset_schema import MetricAnnotation, MultiTurnSample, SingleTurnSample
from ragas.executor import is_event_loop_running
from ragas.losses import BinaryMetricLoss, MSELoss
from ragas.prompt import FewShotPydanticPrompt, PromptMixin
from ragas.run_config import RunConfig
from ragas.utils import camel_to_snake, deprecated, get_metric_language

if t.TYPE_CHECKING:
    from langchain_core.callbacks import Callbacks

    from ragas.config import DemonstrationConfig, InstructionConfig
    from ragas.embeddings import BaseRagasEmbeddings
    from ragas.llms import BaseRagasLLM

logger = logging.getLogger(__name__)


VALID_COLUMNS = [
    "user_input",
    "retrieved_contexts",
    "reference_contexts",
    "response",
    "reference",
    "rubric",
]


class MetricType(Enum):
    """
    Enumeration of metric types in Ragas.

    Attributes
    ----------
    SINGLE_TURN : str
        Represents a single-turn metric type.
    MULTI_TURN : str
        Represents a multi-turn metric type.
    """

    SINGLE_TURN = "single_turn"
    MULTI_TURN = "multi_turn"


class MetricOutputType(Enum):
    BINARY = "binary"
    DISCRETE = "discrete"
    CONTINUOUS = "continuous"
    RANKING = "ranking"


@dataclass
class Metric(ABC):
    """
    Abstract base class for metrics in Ragas.

    Attributes
    ----------
    name : str
        The name of the metric.
    required_columns : Dict[str, Set[str]]
        A dictionary mapping metric type names to sets of required column names. This is
        a property and raises `ValueError` if columns are not in `VALID_COLUMNS`.
    """

    _required_columns: t.Dict[MetricType, t.Set[str]] = field(default_factory=dict)
    name: str = field(default="", repr=True)

    def __post_init__(self):
        self.name = self.name or camel_to_snake(self.__class__.__name__)

    def _validate_columns(self, columns: t.Set[str]) -> None:
        """Validate that all columns are valid"""
        invalid_columns = columns - set(VALID_COLUMNS)
        if invalid_columns:
            raise ValueError(
                f"Invalid columns: {invalid_columns}. Must be one of {VALID_COLUMNS}"
            )

    def _process_columns(self, columns: t.Set[str], include_optional: bool = False) -> t.Set[str]:
        """Process column names, optionally including optional columns"""
        if include_optional:
            return {
                col[:-len(":optional")] if col.endswith(":optional") else col
                for col in columns
            }
        return {col for col in columns if not col.endswith(":optional")}

    @property
    def required_columns(self) -> t.Dict[str, t.Set[str]]:
        return {
            k.name: self._process_columns(v)
            for k, v in self._required_columns.items()
        }

    @required_columns.setter 
    def required_columns(self, required_columns: t.Dict[MetricType, t.Set[str]]):
        for columns in required_columns.values():
            self._validate_columns(columns)
        self._required_columns = required_columns

    def get_required_columns(self, with_optional: bool = False) -> t.Dict[str, t.Set[str]]:
        return {
            k.name: self._process_columns(v, with_optional)
            for k, v in self._required_columns.items()
        }

    @abstractmethod
    def init(self, run_config: RunConfig): ...

    @deprecated("0.2", removal="0.3", alternative="single_turn_ascore")
    def score(self, row: t.Dict, callbacks: Callbacks = None) -> float:
        """
        Calculates the score for a single row of data.

        Note
        ----
        This method is deprecated and will be removed in 0.3. Please use `single_turn_ascore` or `multi_turn_ascore` instead.
        """
        callbacks = callbacks or []
        rm, group_cm = new_group(
            self.name,
            inputs=row,
            callbacks=callbacks,
            metadata={"type": ChainType.METRIC},
        )
        try:
            if is_event_loop_running():
                try:
                    import nest_asyncio

                    nest_asyncio.apply()
                except ImportError:
                    raise ImportError(
                        "It seems like your running this in a jupyter-like environment. Please install nest_asyncio with `pip install nest_asyncio` to make it work."
                    )
            loop = asyncio.get_event_loop()
            score = loop.run_until_complete(self._ascore(row=row, callbacks=group_cm))
        except Exception as e:
            if not group_cm.ended:
                rm.on_chain_error(e)
            raise e
        else:
            if not group_cm.ended:
                rm.on_chain_end({"output": score})
        return score

    @deprecated("0.2", removal="0.3", alternative="single_turn_ascore")
    async def ascore(
        self,
        row: t.Dict,
        callbacks: Callbacks = None,
        timeout: t.Optional[float] = None,
    ) -> float:
        """
        Asynchronously calculates the score for a single row of data.

        Note
        ----
        This method is deprecated and will be removed in 0.3. Please use `single_turn_ascore` instead.
        """
        callbacks = callbacks or []
        rm, group_cm = new_group(
            self.name,
            inputs=row,
            callbacks=callbacks,
            metadata={"type": ChainType.METRIC},
        )
        try:
            score = await asyncio.wait_for(
                self._ascore(row=row, callbacks=group_cm),
                timeout=timeout,
            )
        except Exception as e:
            if not group_cm.ended:
                rm.on_chain_error(e)
            raise e
        else:
            if not group_cm.ended:
                rm.on_chain_end({"output": score})
        return score

    async def _ascore(self, row: t.Dict, callbacks: Callbacks) -> float:
        raise NotImplementedError(
            f"Metric '{self.name}' has no implementation for _ascore. score() is deprecated and will be removed in 0.3. Please use single_turn_ascore or multi_turn_ascore instead."
        )


from dataclasses import dataclass
from typing import Optional as t_Optional
@dataclass
class MetricWithLLM(Metric, PromptMixin):
    """
    A metric class that uses a language model for evaluation.

    Attributes
    一个使用语言模型进行评估的度量类。
    属性
    ----------
    llm : Optional[BaseRagasLLM]
        The language model used for the metric.
    """

    llm: t.Optional[BaseRagasLLM] = None
    output_type: t.Optional[MetricOutputType] = None

    llm : t_Optional[BaseRagasLLM]
        用于度量的语言模型。
    output_type : t_Optional[MetricOutputType]
        度量输出类型。
    """
    llm: t_Optional[BaseRagasLLM] = None
    output_type: t_Optional[MetricOutputType] = None
    def init(self, run_config: RunConfig):
        # 检查是否提供了有效的语言模型
        if self.llm is None:
            raise ValueError(
                f"Metric '{self.name}' has no valid LLM provided (self.llm is None). Please initantiate a the metric with an LLM to run."  # noqa
                f"度量 '{self.name}' 没有提供有效的语言模型 (self.llm is None). 请使用语言模型实例化度量以运行。"
            )
        # 设置运行配置
        self.llm.set_run_config(run_config)

    def _optimize_instruction(
        self,
        instruction_config: InstructionConfig,
        dataset: MetricAnnotation,
        callbacks: Callbacks,
        run_config: RunConfig,
        batch_size: t.Optional[int],
        with_debugging_logs: bool,
        raise_exceptions: bool,
    ):
        if self.llm is None:
            raise ValueError(
                f"Metric '{self.name}' has no valid LLM provided (self.llm is None). Please initantiate a the metric with an LLM to run."  # noqa
            )
        optimizer = instruction_config.optimizer
        if optimizer.llm is None:
            optimizer.llm = instruction_config.llm

        # figure out the loss function
        if instruction_config.loss is None:
            if self.output_type is None:
                raise ValueError(
                    f"Output type for metric '{self.name}' is not defined. Please set the output type in the metric or in the instruction config."
                )
            if self.output_type.name == MetricOutputType.BINARY.name:
                loss_fun = BinaryMetricLoss()
            elif (
                self.output_type.name == MetricOutputType.CONTINUOUS.name
                or self.output_type.name == MetricOutputType.DISCRETE.name
            ):
                loss_fun = MSELoss()
            else:
                raise NotImplementedError(
                    f"Output type '{self.output_type.name}' not implemented"
                )
        else:
            loss_fun = instruction_config.loss

        # Optimize the prompts
        optimizer.metric = self
        optimizer_config = instruction_config.optimizer_config or {}
        optimized_prompts = optimizer.optimize(
            dataset[self.name],
            loss_fun,
            optimizer_config,
            callbacks=callbacks,
            run_config=run_config,
            batch_size=batch_size,
            with_debugging_logs=with_debugging_logs,
            raise_exceptions=raise_exceptions,
        )

        # replace the instruction in the metric with the optimized instruction
        prompts = self.get_prompts()
        for key, val in optimized_prompts.items():
            prompts[key].instruction = val
        self.set_prompts(**prompts)

    def _optimize_demonstration(
        self, demonstration_config: DemonstrationConfig, dataset: MetricAnnotation
    ):
        # get the prompt annotations for this metric
        prompt_annotations = dataset[self.name].get_prompt_annotations()
        prompts = self.get_prompts()
        for prompt_name, prompt_annotation_list in prompt_annotations.items():
            # create a new FewShotPydanticPrompt with these annotations
            if prompt_name not in prompts:
                raise ValueError(
                    f"Prompt '{prompt_name}' not found in metric '{self.name}'. Please check the prompt names in the annotation dataset."
                )
            pydantic_prompt = prompts[prompt_name]
            input_model, output_model = (
                pydantic_prompt.input_model,
                pydantic_prompt.output_model,
            )
            # convert annotations into examples
            input_examples, output_examples = [], []
            for i, prompt_annotation in enumerate(prompt_annotation_list):
                try:
                    input_examples.append(
                        input_model.model_validate(prompt_annotation.prompt_input)
                    )
                    # use the edited output if it is provided
                    if prompt_annotation.edited_output is not None:
                        output_examples.append(
                            output_model.model_validate(prompt_annotation.edited_output)
                        )
                    else:
                        output_examples.append(
                            output_model.model_validate(prompt_annotation.prompt_output)
                        )
                except ValidationError as e:
                    logger.warning(
                        f"Skipping prompt '{prompt_name}' example {i} because of validation error: {e}"
                    )
                    continue
            embedding_model = demonstration_config.embedding
            few_shot_prompt = FewShotPydanticPrompt.from_pydantic_prompt(
                pydantic_prompt=pydantic_prompt,
                embeddings=embedding_model,
            )

            # add the top k examples to the few shot prompt
            few_shot_prompt.top_k_for_examples = demonstration_config.top_k
            few_shot_prompt.threshold_for_examples = demonstration_config.threshold

            # add examples to the few shot prompt
            for input_example, output_example in tqdm(
                zip(input_examples, output_examples),
                total=len(input_examples),
                desc=f"Few-shot examples [{prompt_name}]",
            ):
                few_shot_prompt.add_example(input_example, output_example)
            prompts[prompt_name] = few_shot_prompt
        self.set_prompts(**prompts)

    def train(
        self,
        path: t.Optional[str] = None,
        run_id: t.Optional[str] = None,
        demonstration_config: t.Optional[DemonstrationConfig] = None,
        instruction_config: t.Optional[InstructionConfig] = None,
        callbacks: t.Optional[Callbacks] = None,
        run_config: t.Optional[RunConfig] = None,
        batch_size: t.Optional[int] = None,
        with_debugging_logs=False,
        raise_exceptions: bool = True,
    ) -> None:
        """
        Train the metric using local JSON data or annotations from Ragas platform

        Parameters
        ----------
        path : str, optional
            Path to local JSON training data file
        run_id : str, optional
            Direct run ID to fetch annotations
        demonstration_config : DemonstrationConfig, optional
            Configuration for demonstration optimization
        instruction_config : InstructionConfig, optional
            Configuration for instruction optimization
        callbacks : Callbacks, optional
            List of callback functions
        run_config : RunConfig, optional
            Run configuration
        batch_size : int, optional
            Batch size for training
        with_debugging_logs : bool, default=False
            Enable debugging logs
        raise_exceptions : bool, default=True
            Whether to raise exceptions during training

        Raises
        ------
        ValueError
            If invalid combination of path, and run_id is provided
        """
        # Validate input parameters
        provided_inputs = sum(x is not None for x in [path, run_id])
        if provided_inputs == 0:
            raise ValueError("One of path or run_id must be provided")
        if provided_inputs > 1:
            raise ValueError("Only one of path or run_id should be provided")

        run_config = run_config or RunConfig()
        callbacks = callbacks or []

        # Load the dataset based on input type
        if path is not None:
            if not path.endswith(".json"):
                raise ValueError("Train data must be in json format")
            dataset = MetricAnnotation.from_json(path, metric_name=self.name)
        elif run_id is not None:
            dataset = MetricAnnotation.from_app(
                run_id=run_id,
                metric_name=self.name,
            )
        else:
            raise ValueError("One of path or run_id must be provided")

        # only optimize the instruction if instruction_config is provided
        if instruction_config is not None:
            self._optimize_instruction(
                instruction_config=instruction_config,
                dataset=dataset,
                callbacks=callbacks,
                run_config=run_config,
                batch_size=batch_size,
                with_debugging_logs=with_debugging_logs,
                raise_exceptions=raise_exceptions,
            )

        # if demonstration_config is provided, optimize the demonstrations
        if demonstration_config is not None:
            self._optimize_demonstration(
                demonstration_config=demonstration_config,
                dataset=dataset,
            )


@dataclass
class MetricWithEmbeddings(Metric):
    embeddings: t.Optional[BaseRagasEmbeddings] = None

    def init(self, run_config: RunConfig):
        if self.embeddings is None:
            raise ValueError(
                f"Metric '{self.name}' has no valid embeddings provided (self.embeddings is None). Please initantiate a the metric with an embeddings to run."  # noqa
            )
        self.embeddings.set_run_config(run_config)


def _common_score(
    self,
    sample: t.Union[SingleTurnSample, MultiTurnSample],
    ascore_method: t.Callable,
    callbacks: Callbacks = None,
    timeout: t.Optional[float] = None,
) -> float:
    """
    Common scoring logic for both single-turn and multi-turn metrics.
    
    Parameters
    ----------
    sample : Union[SingleTurnSample, MultiTurnSample]
        The sample to score
    ascore_method : Callable
        The async scoring method to use
    callbacks : Callbacks, optional
        Callback functions
    timeout : float, optional
        Timeout in seconds
        
    Returns
    -------
    float
        The calculated score
    """
    callbacks = callbacks or []
    rm, group_cm = new_group(
        self.name,
        inputs=sample.to_dict(),
        callbacks=callbacks,
        metadata={"type": ChainType.METRIC},
    )
    
    try:
        if is_event_loop_running():
            try:
                import nest_asyncio
                nest_asyncio.apply()
            except ImportError:
                raise ImportError(
                    "Please install nest_asyncio with `pip install nest_asyncio` to run in Jupyter environments."
                )

        if timeout is not None:
            score = asyncio.get_event_loop().run_until_complete(
                asyncio.wait_for(
                    ascore_method(sample=sample, callbacks=group_cm),
                    timeout=timeout
                )
            )
        else:
            score = asyncio.get_event_loop().run_until_complete(
                ascore_method(sample=sample, callbacks=group_cm)
            )
            
    except Exception as e:
        if not group_cm.ended:
            rm.on_chain_error(e)
        raise e
    else:
        if not group_cm.ended:
            rm.on_chain_end({"output": score})

    # Track evaluation event
    _analytics_batcher.add_evaluation(
        EvaluationEvent(
            metrics=[self.name],
            num_rows=1,
            evaluation_type=MetricType.SINGLE_TURN.name,
            language=get_metric_language(self),
        )
    )
    return score


class SingleTurnMetric(Metric):
    """
    A metric class for evaluating single-turn interactions.

    This class provides methods to score single-turn samples, both synchronously and asynchronously.
    """

    def _only_required_columns_single_turn(
        self, sample: SingleTurnSample
    ) -> SingleTurnSample:
        """
        Simplify the sample to only include the required columns.
        """
        required_columns = self.get_required_columns(with_optional=True).get(
            MetricType.SINGLE_TURN.name, set()
        )
        if not required_columns:
            return sample
        return SingleTurnSample(**sample.model_dump(include=required_columns))

    def single_turn_score(
        self,
        sample: SingleTurnSample,
        callbacks: Callbacks = None,
    ) -> float:
        """
        Synchronously score a single-turn sample.

        May raise ImportError if nest_asyncio is not installed in a Jupyter-like environment.
        """
        callbacks = callbacks or []
        # only get the required columns
        sample = self._only_required_columns_single_turn(sample)
        rm, group_cm = new_group(
            self.name,
            inputs=sample.to_dict(),
            callbacks=callbacks,
            metadata={"type": ChainType.METRIC},
        )
        try:
            if is_event_loop_running():
                try:
                    import nest_asyncio

                    nest_asyncio.apply()
                except ImportError:
                    raise ImportError(
                        "It seems like your running this in a jupyter-like environment. Please install nest_asyncio with `pip install nest_asyncio` to make it work."
                    )
            loop = asyncio.get_event_loop()
            score = loop.run_until_complete(
                self._single_turn_ascore(sample=sample, callbacks=group_cm)
            )
        except Exception as e:
            if not group_cm.ended:
                rm.on_chain_error(e)
            raise e
        else:
            if not group_cm.ended:
                rm.on_chain_end({"output": score})

        # track the evaluation event
        _analytics_batcher.add_evaluation(
            EvaluationEvent(
                metrics=[self.name],
                num_rows=1,
                evaluation_type=MetricType.SINGLE_TURN.name,
                language=get_metric_language(self),
            )
        )
        return score

    @deprecated("0.2", removal="0.3", alternative="single_turn_ascore")
    async def ascore(
        self,
        row: t.Dict,
        callbacks: Callbacks = None,
        timeout: t.Optional[float] = None,
    ) -> float:
        """
        Asynchronously calculates the score for a single row of data.

        Note
        ----
        This method is deprecated and will be removed in 0.3. Please use `single_turn_ascore` instead.
        """
        callbacks = callbacks or []
        rm, group_cm = new_group(
            self.name,
            inputs=row,
            callbacks=callbacks,
            metadata={"type": ChainType.METRIC},
        )
        try:
            score = await asyncio.wait_for(
                self._single_turn_ascore(sample=row, callbacks=group_cm),
                timeout=timeout,
            )
        except Exception as e:
            if not group_cm.ended:
                rm.on_chain_error(e)
            raise e
        else:
            if not group_cm.ended:
                rm.on_chain_end({"output": score})
        return score

    async def _single_turn_ascore(
        self,
        sample: SingleTurnSample,
        callbacks: Callbacks,
    ) -> float:
        """
        Abstract method to be implemented by subclasses for actual scoring logic.
        """
        ...


class MultiTurnMetric(Metric):
    """
    A metric class for evaluating multi-turn conversations.

    This class extends the base Metric class to provide functionality
    for scoring multi-turn conversation samples.
    """

    def _only_required_columns_multi_turn(
        self, sample: MultiTurnSample
    ) -> MultiTurnSample:
        """
        Simplify the sample to only include the required columns.
        """
        required_columns = self.get_required_columns(with_optional=True).get(
            MetricType.MULTI_TURN.name, set()
        )
        if not required_columns:
            return sample
        return MultiTurnSample(**sample.model_dump(include=required_columns))

    def multi_turn_score(
        self,
        sample: MultiTurnSample,
        callbacks: Callbacks = None,
    ) -> float:
        """
        Score a multi-turn conversation sample synchronously.

        May raise ImportError if nest_asyncio is not installed in Jupyter-like environments.
        """
        callbacks = callbacks or []
        sample = self._only_required_columns_multi_turn(sample)
        rm, group_cm = new_group(
            self.name,
            inputs=sample.to_dict(),
            callbacks=callbacks,
            metadata={"type": ChainType.METRIC},
        )
        try:
            if is_event_loop_running():
                try:
                    import nest_asyncio

                    nest_asyncio.apply()
                except ImportError:
                    raise ImportError(
                        "It seems like your running this in a jupyter-like environment. Please install nest_asyncio with `pip install nest_asyncio` to make it work."
                    )
            loop = asyncio.get_event_loop()
            score = loop.run_until_complete(
                self._multi_turn_ascore(sample=sample, callbacks=group_cm)
            )
        except Exception as e:
            if not group_cm.ended:
                rm.on_chain_error(e)
            raise e
        else:
            if not group_cm.ended:
                rm.on_chain_end({"output": score})

        # track the evaluation event
        _analytics_batcher.add_evaluation(
            EvaluationEvent(
                metrics=[self.name],
                num_rows=1,
                evaluation_type=MetricType.SINGLE_TURN.name,
                language=get_metric_language(self),
            )
        )
        return score

    @deprecated("0.2", removal="0.3", alternative="single_turn_ascore")
    async def ascore(
        self,
        row: t.Dict,
        callbacks: Callbacks = None,
        timeout: t.Optional[float] = None,
    ) -> float:
        """
        Asynchronously calculates the score for a single row of data.

        Note
        ----
        This method is deprecated and will be removed in 0.3. Please use `single_turn_ascore` instead.
        """
        callbacks = callbacks or []
        rm, group_cm = new_group(
            self.name,
            inputs=row,
            callbacks=callbacks,
            metadata={"type": ChainType.METRIC},
        )
        try:
            score = await asyncio.wait_for(
                self._multi_turn_ascore(sample=row, callbacks=group_cm),
                timeout=timeout,
            )
        except Exception as e:
            if not group_cm.ended:
                rm.on_chain_error(e)
            raise e
        else:
            if not group_cm.ended:
                rm.on_chain_end({"output": score})
        return score

    async def _multi_turn_ascore(
        self,
        sample: MultiTurnSample,
        callbacks: Callbacks,
    ) -> float:
        """
        Abstract method to be implemented by subclasses for actual multi-turn scoring logic.
        """
        ...


class Ensember:
    """Combine multiple LLM outputs for same input to a single output using majority voting."""
    
    def from_discrete(self, inputs: t.Union[list[t.Dict], list[list[t.Dict]]], attribute: str) -> t.List[t.Dict]:
        """
        Simple majority voting for discrete values.
        
        Parameters
        ----------
        inputs : Union[List[Dict], List[List[Dict]]]
            List of inputs or list of list of inputs to ensemble
        attribute : str
            The attribute to use for voting
            
        Returns
        -------
        List[Dict]
            Aggregated results with majority vote for each input
        """
        # Ensure inputs is a list of lists
        inputs = [inputs] if not isinstance(inputs[0], list) else inputs
        
        # Validate inputs
        if not inputs or not all(len(item) == len(inputs[0]) for item in inputs):
            logger.warning("All inputs must have the same length")
            return inputs[0] if inputs else []
            
        if not all(attribute in item for input_list in inputs for item in input_list):
            logger.warning(f"All inputs must have {attribute} attribute")
            return inputs[0]
            
        if len(inputs) == 1:
            return inputs[0]

        # Calculate majority votes
        return [
            {
                **inputs[0][i],
                attribute: Counter(
                    inputs[k][i][attribute] for k in range(len(inputs))
                ).most_common(1)[0][0]
            }
            for i in range(len(inputs[0]))
        ]


@t.runtime_checkable
class ModeMetric(t.Protocol):
    name: str
    mode: str


ensembler = Ensember()
