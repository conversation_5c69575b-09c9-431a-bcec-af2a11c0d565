from __future__ import annotations

import logging
import typing as t
from dataclasses import dataclass, field

import numpy as np

from ragas.dataset_schema import SingleTurnSample
from ragas.embeddings.base import HuggingfaceEmbeddings
from ragas.metrics.base import (
    MetricOutputType,
    MetricType,
    MetricWithEmbeddings,
    SingleTurnMetric,
)

if t.TYPE_CHECKING:
    from langchain_core.callbacks.base import Callbacks


logger = logging.getLogger(__name__)


@dataclass
class SemanticSimilarity(MetricWithEmbeddings, SingleTurnMetric):
    """
    计算参考答案和生成答案的语义相似度，支持普通 embedding 和 cross-encoder。
    推荐使用 cross-encoder 模型获得更高质量的分数。
    参考论文：SAS https://arxiv.org/pdf/2108.06130.pdf

    Attributes
    ----------
    name : str
        指标名称
    is_cross_encoder : bool
        是否为 cross-encoder
    threshold : Optional[float]
        二值化输出的阈值，默认 None
    """

    name: str = "semantic_similarity"
    _required_columns: t.Dict[MetricType, t.Set[str]] = field(
        default_factory=lambda: {MetricType.SINGLE_TURN: {"reference", "response"}}
    )
    output_type: MetricOutputType = MetricOutputType.CONTINUOUS
    is_cross_encoder: bool = False
    threshold: t.Optional[float] = None

    def __post_init__(self):
        # 仅 cross-encoder 模型支持特殊处理
        if isinstance(self.embeddings, HuggingfaceEmbeddings):
            self.is_cross_encoder = bool(getattr(self.embeddings, "is_cross_encoder", False))
            self.embeddings.encode_kwargs = {
                **getattr(self.embeddings, "encode_kwargs", {}),
            }

    async def _single_turn_ascore(
        self, sample: SingleTurnSample, callbacks: t.Any
    ) -> float:
        row = sample.to_dict()
        return await self._ascore(row, callbacks)

    async def _ascore(self, row: t.Dict, callbacks: t.Any) -> float:
        assert self.embeddings is not None, f"Error: '{self.name}' requires embeddings to be set."

        ground_truth = str(row["reference"]).strip() or " "
        answer = str(row["response"]).strip() or " "

        if self.is_cross_encoder and isinstance(self.embeddings, HuggingfaceEmbeddings):
            raise NotImplementedError(
                "async score [ascore()] not implemented for HuggingFace cross-encoder embeddings"
            )
        else:
            embedding_1 = np.array(await self.embeddings.embed_text(ground_truth))
            embedding_2 = np.array(await self.embeddings.embed_text(answer))
            # 防止除零
            norms_1 = np.linalg.norm(embedding_1, keepdims=True) + 1e-8
            norms_2 = np.linalg.norm(embedding_2, keepdims=True) + 1e-8
            embedding_1_normalized = embedding_1 / norms_1
            embedding_2_normalized = embedding_2 / norms_2
            similarity = embedding_1_normalized @ embedding_2_normalized.T
            score = similarity.flatten()

        assert isinstance(score, np.ndarray), "Expects ndarray"
        if self.threshold is not None:
            score = score >= self.threshold

        return float(score.item())


class AnswerSimilarity(SemanticSimilarity):
    name: str = "answer_similarity"

    async def _ascore(self, row: t.Dict, callbacks: t.Any) -> float:
        return await super()._ascore(row, callbacks)


answer_similarity = AnswerSimilarity()
