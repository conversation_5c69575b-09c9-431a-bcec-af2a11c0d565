from __future__ import annotations

import logging
import typing as t
from functools import lru_cache
from typing import Union, Optional, Sequence, Any

import numpy as np

# Import the main RagasException from the exceptions module
from ragas.exceptions import RagasException

logger = logging.getLogger(__name__)


def validate_numeric_inputs(*args: Any, allow_negative: bool = True) -
    """
    Validate that all inputs are numeric (int or float).

    Args:
        *args: Variable number of arguments to validate
        allow_negative: Whether to allow negative values

    Raises:
        ValueError: If any input is not numeric or negative (when not allowed)
    """
    for i, arg in enumerate(args)：
        if not isinstance(arg, (int, float)):
            raise ValueError(f"Argument {i+1} must be numeric (
            if not allow_negative and arg < 0:
            raise ValueError(f"Argument {i+1} must be non-negative, got {arg}")


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers, returning default if denominator is zero.

    Args:
        numerator: The numerator
        denominator: The denominator
        default: Value to return if denominator is zero

    Returns:
        float: The division result or default value
    """
    return numerator / denominator if denominator != 0 else default


def weighted_average(values: Sequence[float], weights: Optional[Sequence[float]] = None) -> float:
    """
    Calculate the weighted average of a sequence of values.

    Args:
        values: Sequence of numeric values
        weights: Optional sequence of weights. If None, uniform weights are used.

    Returns:
        float: The weighted average

    Raises:
        ValueError: If values and weights have different lengths or if all weights are zero

    Examples:
        >>> weighted_average([1, 2, 3])
        2.0
        >>> weighted_average([1, 2, 3], [1, 2, 3])
        2.3333333333333335
    """
    if not values:
        return 0.0
    
    values_array = np.array(values)
    
    if weights is None:
        return float(np.mean(values_array))
    
    weights_array = np.array(weights)
    
    if len(values) != len(weights):
        raise ValueError(f"Values and weights must have same length: {len(values)} vs {len(weights)}")
    
    if np.sum(weights_array) == 0:
        raise ValueError("Sum of weights cannot be zero")
    
    return float(np.average(values_array, weights=weights_array))


@lru_cache(maxsize=1024)
def fbeta_score(
    tp: Union[int, float],
    fp: Union[int, float],
    fn: Union[int, float],
    beta: float = 1.0
) -> float:
    """
    Calculate the F-beta score, a weighted harmonic mean of precision and recall.

    The F-beta score is calculated as:
    F_beta = (1 + beta²) * (precision * recall) / ((beta² * precision) + recall)

    Where:
    - precision = tp / (tp + fp)
    - recall = tp / (tp + fn)
    - beta controls the weight of precision vs recall

    Args:
        tp: Number of true positives
        fp: Number of false positives
        fn: Number of false negatives
        beta: Weight of precision in harmonic mean. Default is 1.0 (F1 score)
              - beta > 1: Favors recall
              - beta < 1: Favors precision
              - beta = 1: Equal weight (F1 score)

    Returns:
        float: The calculated F-beta score (0.0 to 1.0)

    Raises:
        ValueError: If inputs are not numeric or beta is negative

    Examples:
        >>> fbeta_score(10, 2, 3)  # F1 score
        0.8333333333333334
        >>> fbeta_score(10, 2, 3, beta=2.0)  # Favor recall
        0.8620689655172413
        >>> fbeta_score(10, 2, 3, beta=0.5)  # Favor precision
        0.8064516129032258
    """
    # Validate inputs
    validate_numeric_inputs(tp, fp, fn, beta, allow_negative=False)
    if beta <= 0:
        raise ValueError("Beta must be positive")

    # Calculate precision and recall using safe division
    precision = safe_divide(tp, tp + fp)
    recall = safe_divide(tp, tp + fn)

    # Early return if both precision and recall are zero
    if precision == 0 and recall == 0:
        return 0.0

    # Calculate F-beta score
    beta_squared = beta * beta  # More efficient than beta ** 2
    numerator = (1 + beta_squared) * precision * recall
    denominator = (beta_squared * precision) + recall

    return safe_divide(numerator, denominator)


