from typing import Union, Optional

def fbeta_score(
    tp: Union[int, float],
    fp: Union[int, float],
    fn: Union[int, float],
    beta: float = 1.0
) -> float:
    """
    Calculate the F-beta score, a weighted harmonic mean of precision and recall.
    
    Args:
        tp: Number of true positives
        fp: Number of false positives
        fn: Number of false negatives
        beta: Weight of precision in harmonic mean. Default is 1.0 (F1 score)
    
    Returns:
        float: The calculated F-beta score
    """
    if not all(isinstance(x, (int, float)) for x in [tp, fp, fn, beta]):
        raise ValueError("All inputs must be numeric (int or float)")
    if beta < 0:
        raise ValueError("Beta must be positive")

    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    
    beta_squared = beta ** 2
    if precision + recall == 0:
        return 0.0
    
    fbeta = (
        (1 + beta_squared)
        * (precision * recall)
        / ((beta_squared * precision) + recall)
    )
    return fbeta


class RagasException(Exception):
    """
    Base exception class for ragas.
    
    This exception class serves as the parent class for all custom exceptions
    in the ragas library, allowing for specific error handling and consistent
    error messaging throughout the codebase.
    
    Args:
        message (str): The error message to be displayed
    """
    
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


