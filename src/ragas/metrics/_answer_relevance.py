from __future__ import annotations

import asyncio
import logging
from dataclasses import dataclass, field
from functools import lru_cache
from typing import Any, Dict, List, Optional, Sequence, Set, Tuple, cast

import numpy as np
from numpy.typing import NDArray
from pydantic import BaseModel, Field, validator

from ragas.dataset_schema import SingleTurnSample
from ragas.metrics.base import (
    MetricOutputType,
    MetricType,
    MetricWithEmbeddings,
    MetricWithLLM,
    SingleTurnMetric,
)
from ragas.prompt import PydanticPrompt
# Constants
DEFAULT_CACHE_SIZE: int = 128
DEFAULT_STRICTNESS: int = 3
DEFAULT_MAX_RETRIES: int = 2
DEFAULT_PARALLEL_WORKERS: int = 4
DEFAULT_SIMILARITY_THRESHOLD: float = 0.1
MIN_CACHE_SIZE: int = 16
MAX_PARALLEL_WORKERS: int = 8
NONCOMMITTAL_PHRASES = ["不知道", "不确定", "无法", "不能", "抱歉"]

# Type aliases
FloatArray = NDArray[np.float32]
Callbacks = Any  # from langchain_core.callbacks

# Configure logging
logger = logging.getLogger(__name__)


class ResponseRelevanceOutput(BaseModel):
    """模型对回答相关性的输出
    属性:
        question: 从回答中生成的问题
        noncommittal: 标记答案是否含糊不清，1表示含糊不清，0表示明确
    """
    question: str = Field(description="从回答中生成的问题")
    noncommittal: int = Field(
        description="标记答案是否含糊不清，1表示含糊不清，0表示明确",
        ge=0, 
        le=1
    )
    
    @validator("question")
    def validate_question(cls, v: str) -> str:
        """验证问题不为空"""
        if not v or not v.strip():
            raise ValueError("生成的问题不能为空")
        return v.strip()


class ResponseRelevanceInput(BaseModel):
    """输入到相关性评估的回答内容
    
    属性:
        response: 需要评估相关性的回答文本
    """
    response: str = Field(description="需要评估相关性的回答文本")
    @validator("response")
    def validate_response(cls, v: str) -> str:
        """验证回答不为空"""
        if not v or not v.strip():
            raise ValueError("回答文本不能为空")
        return v


class ResponseRelevancePrompt(
    PydanticPrompt[ResponseRelevanceInput, ResponseRelevanceOutput]
):
    """评估回答相关性的提示模板"""
    instruction = """从给定的回答中生成一个问题，并判断回答是否含糊不清。如果回答含糊不清，noncommittal为1，否则为0。含糊不清的回答通常是模糊、回避或不明确的。例如，"我不知道"或"我不确定"属于含糊不清的回答。"""
    input_model = ResponseRelevanceInput
    output_model = ResponseRelevanceOutput
    examples = [
        (
            ResponseRelevanceInput(
                response="""Albert Einstein was born in Germany.""",
            ),
            ResponseRelevanceOutput(
                question="Where was Albert Einstein born?",
                noncommittal=0,
            ),
        ),
        (
            ResponseRelevanceInput(
                response="""I don't know about the groundbreaking feature of the smartphone invented in 2023 as I am unaware of information beyond 2022.""",
            ),
            ResponseRelevanceOutput(
                question="What was the groundbreaking feature of the smartphone invented in 2023?",
                noncommittal=1,
            ),
        ),
    ]


@dataclass
class ResponseRelevancy(MetricWithLLM, MetricWithEmbeddings, SingleTurnMetric):
    """
    评估回答与给定问题的相关性。
    
    该指标通过对回答生成相应问题，并计算这些生成问题与原始问题的语义相似度来工作。
    包含不完整、冗余或不必要信息的答案会被降低评分。
    得分范围从0到1，1分表示最佳相关性。

    属性
    ----------
    name: str
        指标名称
    strictness: int
        每个回答生成的问题数量，影响评估严格程度
    embeddings: Embedding
        嵌入模型对象，用于计算语义相似度
    question_generation: PydanticPrompt
        用于生成问题的提示模板
    cache_size: int
        嵌入计算的缓存大小
    max_retries: int
        LLM调用失败时的最大重试次数
    parallel_workers: int
        并行处理的最大工作线程数，用于控制并发请求数量
    similarity_threshold: float
        相似度阈值，低于此值的问题被视为不相关
    """

    name: str = "answer_relevancy"
    _required_columns: Dict[MetricType, Set[str]] = field(
        default_factory=lambda: {
            MetricType.SINGLE_TURN: {
                "user_input",
                "response",
            }
        }
    )
    output_type = MetricOutputType.CONTINUOUS

    question_generation: PydanticPrompt = field(default_factory=ResponseRelevancePrompt)
    strictness: int = 3
    cache_size: int = 128
    max_retries: int = 2
    parallel_workers: int = 4
    similarity_threshold: float = 0.1
    use_fallback: bool = True

    def __post_init__(self):
        """初始化和验证参数，设置嵌入计算的缓存"""
        # 验证参数
        if self.strictness < 1:
            logger.warning(f"无效的strictness值: {self.strictness}，已设置为默认值1")
            self.strictness = 1
            
        if self.cache_size < 16:
            logger.warning(f"缓存大小过小: {self.cache_size}，已设置为默认值16")
            self.cache_size = 16
            
        # 设置嵌入计算的LRU缓存，提高性能
        self._calculate_similarity = lru_cache(maxsize=self.cache_size)(
            self._calculate_similarity_implementation
        )
        
        # 限制并行工作线程数量
        self.parallel_workers = min(max(1, self.parallel_workers), 8)

    def _normalize_vector(self, vector: FloatArray) -> FloatArray:
        """标准化向量，处理零向量的情况。"""
        norm = np.linalg.norm(vector)
        if norm > np.finfo(float).eps:
            return vector / norm
        return vector

    def _get_embedding(self, text: str) -> FloatArray:
        """获取文本的嵌入向量。

        Args:
            text: 输入文本

        Returns:
            FloatArray: 嵌入向量
        """
        if not self.embeddings:
            raise ValueError(f"错误: '{self.name}' 需要设置嵌入模型")
        return np.asarray(
            self.embeddings.embed_query(text), 
            dtype=np.float32
        ).reshape(1, -1)

    def _get_batch_embeddings(self, texts: List[str]) -> FloatArray:
        """批量获取文本的嵌入向量。

        Args:
            texts: 输入文本列表

        Returns:
            FloatArray: 嵌入向量矩阵
        """
        if not self.embeddings:
            raise ValueError(f"错误: '{self.name}' 需要设置嵌入模型")
        return np.asarray(
            self.embeddings.embed_documents(texts),
            dtype=np.float32
        ).reshape(len(texts), -1)

    def _calculate_similarity_implementation(
        self, 
        question: str, 
        generated_questions: Tuple[str, ...]
    ) -> FloatArray:
        """计算原始问题与生成问题之间的余弦相似度。

        Args:
            question: 原始问题
            generated_questions: 生成的问题元组（用于缓存）

        Returns:
            FloatArray: 相似度数组
        """
        assert self.embeddings is not None, f"错误: '{self.name}' 需要设置嵌入模型。"
        
        if not question or not generated_questions:
            return np.array([], dtype=np.float32)

        try:
            # 过滤无效问题
            valid_questions = [
                q for q in generated_questions 
                if q and isinstance(q, str) and q.strip()
            ]
            if not valid_questions:
                return np.array([], dtype=np.float32)

            # 获取嵌入向量
            question_vec = np.asarray(self.embeddings.embed_query(question), dtype=np.float32).reshape(1, -1)
            gen_question_vecs = np.asarray(
                [self.embeddings.embed_query(q) for q in valid_questions],
                dtype=np.float32
            ).reshape(len(valid_questions), -1)

            # 使用矢量化运算计算余弦相似度
            # 预先计算向量范数
            q_norm = np.linalg.norm(question_vec, axis=1)
            gen_norms = np.linalg.norm(gen_question_vecs, axis=1)

            # 计算余弦相似度
            similarities = np.dot(gen_question_vecs, question_vec.T).flatten() / (q_norm * gen_norms)

            return similarities
            
        except Exception as e:
            logger.error(f"计算余弦相似度时出错: {e}", exc_info=True)
            return np.zeros(len(generated_questions), dtype=np.float32)

    def calculate_similarity(
        self, question: str, generated_questions: list[str]
    ) -> np.ndarray:
        """
        计算原始问题与生成问题之间的余弦相似度（带缓存）
        
        参数
        ----------
        question: str
            原始问题
        generated_questions: list[str]
            生成的问题列表
            
        返回
        ----------
        np.ndarray
            原始问题与每个生成问题的余弦相似度
        """
        # 检查输入有效性
        if not question or not generated_questions:
            return np.array([], dtype=np.float32)
            
        # 过滤空字符串和非字符串值
        valid_questions = [q for q in generated_questions if q and isinstance(q, str)]
        if not valid_questions:
            return np.array([], dtype=np.float32)
            
        # 将列表转换为元组以支持缓存
        return self._calculate_similarity(question, tuple(valid_questions))

    def _calculate_score(
        self, answers: Sequence[ResponseRelevanceOutput], row: Dict
    ) -> float:
        """
        根据生成的问题和原始问题的相似度计算相关性得分
        
        参数
        ----------
        answers: Sequence[ResponseRelevanceOutput]
            包含生成问题和非确定性标志的模型输出序列
        row: Dict
            包含原始问题和回答的数据行
            
        返回
        ----------
        float
            相关性得分，范围为0到1
        """
        question = row.get("user_input", "")
        if not question:
            logger.warning("计算相关性得分时缺少原始问题")
            return float("nan")
        
        # 提取并验证生成的问题
        gen_questions = [answer.question for answer in answers if hasattr(answer, "question")]
        
        # 检查回答是否含糊不清
        is_noncommittal = any(
            getattr(answer, "noncommittal", 0) 
            for answer in answers 
            if hasattr(answer, "noncommittal")
        )
        
        # 验证生成的问题有效性
        if not gen_questions:
            logger.warning("无效的JSON响应或生成的问题为空")
            return float("nan")
            
        # 筛选出非空的问题
        valid_questions = [q for q in gen_questions if q and isinstance(q, str) and q.strip()]
        if not valid_questions:
            logger.warning("所有生成的问题都为空或无效")
            return float("nan")
            
        # 计算余弦相似度
        cosine_sim = self.calculate_similarity(question, valid_questions)
        
        # 过滤掉低于阈值的相似度以排除噪音
        if len(cosine_sim) > 0:
            cosine_sim = cosine_sim[cosine_sim >= self.similarity_threshold]
            
        # 如果所有相似度都低于阈值，则使用原始相似度
        if len(cosine_sim) == 0 and valid_questions:
            cosine_sim = self.calculate_similarity(question, valid_questions)
        
        # 如果仍然没有有效相似度，返回0
        if len(cosine_sim) == 0:
            logger.warning("无法计算有效的相似度")
            return 0.0
        
        # 如果回答含糊不清，得分为0；否则为相似度平均值
        mean_sim = float(cosine_sim.mean())
        score = mean_sim * (0 if is_noncommittal else 1)
        # 详细日志记录
        logger.debug(
            f"回答相关性得分: {score:.4f} (相似度={mean_sim:.4f}, 非确定性={is_noncommittal})"
        )
        return score

    async def _single_turn_ascore(
        self, sample: SingleTurnSample, callbacks: Optional[Callbacks] = None
    ) -> float:
        """计算单轮对话的相关性得分"""
        row = sample.to_dict()
        return await self._ascore(row, callbacks)
    async def _generate_question_with_retry(
        self,
        prompt_input: ResponseRelevanceInput,
        callbacks: Optional[Callbacks] = None
    ) -> Optional[ResponseRelevanceOutput]:
        """尝试生成问题，支持重试。

        Args:
            prompt_input: 输入提示
            callbacks: 可选的回调函数

        Returns:
            Optional[ResponseRelevanceOutput]: 生成的问题输出，失败返回None
        """
        for attempt in range(self.max_retries + 1):
            try:
                return await self.question_generation.generate(
                    data=prompt_input,
                    llm=self.llm,
                    callbacks=callbacks,
                )
            except Exception as e:
                if attempt < self.max_retries:
                    logger.warning(
                        "问题生成失败，正在重试 (%d/%d): %s",
                        attempt + 1,
                        self.max_retries,
                        str(e)
                    )
                else:
                    logger.error(
                        "问题生成失败，已达到最大重试次数: %s",
                        str(e)
                    )
        return None

    async def _generate_questions(
        self,
        response: str,
        callbacks: Optional[Callbacks] = None
    ) -> List[ResponseRelevanceOutput]:
        """并行生成多个问题。

        Args:
            response: 回答文本
            callbacks: 可选的回调函数

        Returns:
            List[ResponseRelevanceOutput]: 生成的问题列表
        """
        prompt_input = ResponseRelevanceInput(response=response)
        sem = asyncio.Semaphore(min(self.strictness, self.parallel_workers))

        async def bounded_generate() -> Optional[ResponseRelevanceOutput]:
            async with sem:
                return await self._generate_question_with_retry(
                    prompt_input, callbacks
                )

        tasks = [bounded_generate() for _ in range(self.strictness)]
        responses = await asyncio.gather(*tasks)
        return [r for r in responses if r is not None]

    async def _ascore(
        self,
        row: Dict,
        callbacks: Optional[Callbacks] = None
    ) -> float:
        """计算回答相关性得分。

        Args:
            row: 包含user_input和response的字典
            callbacks: 可选的回调函数
        Returns:
            float: 相关性得分 (0-1)
        """
        if not self.llm:
            raise ValueError("LLM未设置")

        # 验证输入
        question = row.get("user_input", "")
        response = row.get("response", "")
        
        if not question or not response:
            logger.warning("缺少问题或回答，无法评估相关性")
            return float("nan")

        try:
            # 生成问题
            valid_responses = await self._generate_questions(response, callbacks)

            if not valid_responses:
                logger.warning("问题生成失败")
                return await self._fallback_score(question, response)

            # 计算得分
            return self._calculate_score(valid_responses, row)

        except Exception as e:
            logger.error("计算相关性得分时出错: %s", str(e), exc_info=True)
            return float("nan")

    async def _fallback_score(self, question: str, response: str) -> float:
        """当问题生成失败时的后备评分方法。"""
        if not self.use_fallback:
            return float("nan")

        logger.info("使用后备评分方法")
        
        # 检查是否含糊不清
        if any(phrase in response.lower() for phrase in NONCOMMITTAL_PHRASES):
            return 0.0

        try:
            if self.embeddings:
                q_vec = self._get_embedding(question)
                r_vec = self._get_embedding(response)
                
                # 计算余弦相似度
                q_norm = self._normalize_vector(q_vec)
                r_norm = self._normalize_vector(r_vec)
                sim = float(np.dot(q_norm, r_norm.T))
                
                return max(0.0, min(1.0, sim))
                
        except Exception as e:
            logger.error("后备评分失败: %s", str(e))
            
        return float("nan")
class AnswerRelevancy(ResponseRelevancy):
    """
    答案相关性评估指标（ResponseRelevancy的别名类）
    
    此类继承自ResponseRelevancy，提供相同的功能，但使用更通用的名称。
    可用于评估生成式AI回答与用户问题的相关程度，帮助识别离题或包含
    不必要信息的回答。
    """
    name: str = "answer_relevancy"  # 确保名称一致
    
    def __post_init__(self):
        """设置嵌入计算的缓存并初始化参数"""
        super().__post_init__()
        logger.debug(f"初始化答案相关性评估器: strictness={self.strictness}, workers={self.parallel_workers}")
    
    async def _ascore(
        self, row: Dict, callbacks: Optional[Callbacks] = None
    ) -> float:
        """计算回答相关性得分，调用父类方法"""
        return await super()._ascore(row, callbacks)


# 实例化默认的答案相关性评估器
answer_relevancy = AnswerRelevancy()
