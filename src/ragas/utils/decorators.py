import functools
import os
import typing as t
from functools import wraps

# 环境变量与装饰器
DEBUG_ENV_VAR = "RAGAS_DEBUG"

def debug_enabled() -> bool:
    """检查是否启用调试模式。"""
    if not hasattr(debug_enabled, '_cache'):
        debug_enabled._cache = os.environ.get(DEBUG_ENV_VAR, "").lower() in ("1", "true", "yes")
    return debug_enabled._cache

def deprecated(message: str) -> t.Callable:
    """标记函数为已弃用。"""
    def decorator(func: t.Callable) -> t.Callable:
        @functools.wraps(func)
        def wrapper(*args: t.Any, **kwargs: t.Any) -> t.Any:
            import warnings
            if not hasattr(wrapper, '_warned'):
                warnings.warn(
                    f"函数 {func.__name__} 已弃用: {message}",
                    category=DeprecationWarning,
                    stacklevel=2,
                )
                wrapper._warned = True
            return func(*args, **kwargs)
        return wrapper
    return decorator

def singleton(cls: t.Type) -> t.Type:
    """单例模式装饰器，支持线程安全，采用双重检查锁定模式以减少锁争用。"""
    instances: dict = {}
    from threading import Lock
    _lock = Lock()
    
    @wraps(cls)
    def get_instance(*args: t.Any, **kwargs: t.Any) -> t.Any:
        if cls not in instances:
            with _lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    return get_instance
